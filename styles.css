/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    overflow-x: hidden;
}

/* 顶部导航栏 */
.top-header {
    background: white;
    border-bottom: 1px solid #e5e5e5;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 20px;
}


.page-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
}





/* 主容器 */
.main-container {
    display: flex;
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* 左侧导航栏 */
.sidebar {
    width: 200px;
    background-color: #2c3e50;
    color: white;
    padding: 0;
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    overflow-y: auto;
}

.sidebar-nav {
    padding: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 14px;
}

.nav-item:hover {
    background-color: rgba(255,255,255,0.1);
}

.nav-item.active {
    background-color: #FF6B35;
}

.nav-item i {
    width: 16px;
    text-align: center;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 200px;
    padding: 20px;
    background-color: #f5f5f5;
}

/* 统一的Bento Grid卡片系统 */
.bento-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.bento-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

/* 统一的卡片头部 */
.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-header i {
    color: #FF6B35;
}

/* 网红信息卡片 */
.influencer-info-card {
    margin-bottom: 20px;
}

/* 更新时间 */
.update-time {
    font-size: 12px;
    color: #999;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.update-status {
    background-color: #f6ffed;
    color: #52c41a;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

/* 网红内容区域 */
.influencer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.influencer-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #f0f0f0;
}

.basic-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 姓名行 */
.name-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 4px;
}

.name {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.username {
    color: #666;
    font-size: 14px;
}

.category {
    background-color: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 位置行 */
.location-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

.flag {
    width: 16px;
    height: 12px;
    border-radius: 2px;
    object-fit: cover;
}

.country {
    font-size: 14px;
    color: #666;
}

.language {
    font-size: 14px;
    color: #666;
    padding: 2px 8px;
    background-color: #f0f0f0;
    border-radius: 12px;
}

.verified {
    color: #52c41a;
    font-size: 12px;
    font-weight: 500;
}

.subscriber-count {
    background-color: #fff7e6;
    color: #fa8c16;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.flag {
    width: 16px;
    height: 12px;
}

.verified {
    color: #52c41a;
}

/* 右侧操作按钮 */
.influencer-right {
    display: flex;
    align-items: center;
}

.action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
}



/* YouTube按钮 */
.btn-youtube {
    background-color: #ff0000;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.btn-youtube:hover {
    background-color: #cc0000;
    transform: translateY(-1px);
}

/* 关键指标卡片组 */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

.metric-card {
    text-align: center;
    padding: 20px 16px;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.metric-header {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
    line-height: 1.2;
}

.metric-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    line-height: 1.2;
}



/* 数据分类标签卡片 */
.data-tabs-card {
    margin-bottom: 20px;
}

/* 数据标签页 */
.data-tabs-section {
    margin-bottom: 20px;
}

/* 页签内容容器样式 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-buttons {
    display: flex;
    gap: 0;
    border-bottom: 1px solid #f0f0f0;
}

.tab-button {
    padding: 12px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
    border-bottom: 2px solid transparent;
    position: relative;
}

.tab-button:hover {
    color: #FF6B35;
}

.tab-button.active {
    color: #FF6B35;
    border-bottom-color: #FF6B35;
    font-weight: 500;
}

.tab-button i {
    font-size: 16px;
}

/* 基本数据卡片 */
.basic-data-card {
    margin-bottom: 20px;
}

.time-filter {
    background-color: #f0f0f0;
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* 合作价格和CPM区域 */
.cooperation-pricing-section {
    margin-bottom: 30px;
}

.section-header {
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-header i {
    color: #FF6B35;
}

.pricing-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
}

/* CPM卡片样式 */
.cpm-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 200px;
}

.cpm-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin: 15px 0;
}

.cpm-value .currency {
    font-size: 24px;
    font-weight: 600;
    color: #FF6B35;
}

.cpm-value .amount {
    font-size: 36px;
    font-weight: 700;
    color: #FF6B35;
}

.cpm-description {
    font-size: 12px;
    color: #666;
    margin: 8px 0;
    line-height: 1.4;
}

.cpm-note {
    font-size: 11px;
    color: #999;
    line-height: 1.4;
}

/* 植入视频价格卡片样式 */
.video-pricing-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 200px;
}

.pricing-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin: 15px 0;
}

.pricing-value .currency {
    font-size: 20px;
    font-weight: 600;
    color: #FF6B35;
}

.pricing-value .amount-range {
    font-size: 24px;
    font-weight: 700;
    color: #FF6B35;
}

.pricing-description {
    font-size: 12px;
    color: #666;
    margin: 8px 0;
    line-height: 1.4;
}

.pricing-note {
    font-size: 11px;
    color: #999;
    line-height: 1.4;
}

/* 频道粉丝数排名区域 */
.ranking-section {
    margin-bottom: 30px;
}

.ranking-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* 排名卡片样式 */
.ranking-card {
    display: flex;
    align-items: center;
    gap: 15px;
    min-height: 120px;
    padding: 20px;
}

.ranking-icon {
    width: 50px;
    height: 50px;
    background-color: #fff7e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.ranking-icon i {
    font-size: 20px;
    color: #FF6B35;
}

.ranking-content {
    flex: 1;
}

.ranking-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.ranking-value {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.rank-number {
    font-size: 32px;
    font-weight: 700;
    color: #FF6B35;
}

.rank-suffix {
    font-size: 14px;
    color: #999;
}

/* 受众数据页面样式 */
.audience-data-section {
    display: block;
}

/* 受众概览网格 */
.audience-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.audience-stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.audience-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #FF6B35, #FF8A65);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon i {
    color: white;
    font-size: 20px;
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.stat-unit,
.stat-percentage {
    font-size: 14px;
    color: #999;
}

.stat-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.stat-status.excellent {
    background-color: #e8f5e8;
    color: #4caf50;
}

/* 受众分析网格 */
.audience-analysis-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.audience-chart-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.audience-chart-card .card-header {
    margin-bottom: 20px;
}

.audience-chart-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* 受众区域和语言卡片样式 */
.region-card,
.language-card {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 横向条形图区域 */
.horizontal-bar-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.horizontal-bar-container {
    width: 100%;
    padding: 0 92px 0 80px; /* 左右padding与详细列表对齐 */
}

.horizontal-bar {
    display: flex;
    width: 100%;
    height: 24px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bar-segment {
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.bar-segment:hover {
    opacity: 0.8;
    transform: scaleY(1.1);
}

/* 工具提示样式 */
.tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transform: translateX(-50%);
    transition: opacity 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

.tooltip.show {
    opacity: 1;
}

.tooltip-icon {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
    vertical-align: middle;
}

/* 详细列表样式 */
.region-detail-list,
.language-detail-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 10px;
}

.detail-item {
    display: grid;
    grid-template-columns: 80px 1fr 60px;
    align-items: center;
    gap: 12px;
}

.detail-label {
    font-size: 12px;
    color: #666;
    text-align: right;
    font-weight: 500;
}

.detail-bar {
    height: 20px;
    background-color: #f5f5f5;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.detail-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.8s ease;
}

.detail-percentage {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    text-align: center;
}



/* 人口统计网格 */
.demographics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.age-chart-container,
.gender-chart-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

.chart-note {
    font-size: 11px;
    color: #999;
    line-height: 1.4;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

/* 受众数据响应式设计 */
@media (max-width: 1200px) {
    .audience-analysis-grid,
    .demographics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .audience-overview-grid {
        grid-template-columns: 1fr;
    }

    .audience-stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-icon i {
        font-size: 16px;
    }

    .horizontal-bar {
        height: 20px;
    }

    .horizontal-bar-container {
        padding: 0 58px 0 60px; /* 移动端调整padding与详细列表对齐 */
    }

    .detail-item {
        grid-template-columns: 60px 1fr 50px;
        gap: 8px;
    }

    .detail-label {
        font-size: 11px;
    }

    .detail-bar {
        height: 18px;
    }

    .detail-percentage {
        font-size: 11px;
    }
}

/* 人口统计网格 */
.demographics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.age-chart-container,
.gender-chart-container {
    height: 300px;
    position: relative;
}

/* 图表控制和统计 */
.chart-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.total-count {
    font-size: 12px;
    color: #666;
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 12px;
}



/* 性别统计摘要 */
.gender-stats-summary {
    margin: 15px 0;
    border-top: 1px solid #f0f0f0;
    padding-top: 15px;
}

.gender-stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.gender-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.gender-indicator.male {
    background-color: #4285F4;
}

.gender-indicator.female {
    background-color: #EA4335;
}

.gender-label {
    font-size: 12px;
    color: #666;
    min-width: 30px;
}

.gender-percentage {
    font-size: 12px;
    font-weight: 600;
    color: #333;
}

.chart-note {
    font-size: 11px;
    color: #999;
    line-height: 1.4;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

/* 营销分析模块 */
.marketing-analysis-section {
    margin-top: 30px;
}

.marketing-analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

/* 受众反馈卡片 */
.marketing-feedback-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feedback-metrics {
    margin: 20px 0;
}

.feedback-item {
    margin-bottom: 25px;
}

.feedback-item:last-child {
    margin-bottom: 0;
}

.feedback-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.feedback-value {
    font-size: 24px;
    font-weight: 700;
    color: #FF6B35;
    margin-bottom: 10px;
}

.feedback-bar {
    width: 100%;
    height: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.feedback-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
}

.feedback-fill.positive {
    background: linear-gradient(90deg, #4CAF50, #66BB6A);
}

.feedback-fill.interested {
    background: linear-gradient(90deg, #2196F3, #42A5F5);
}

.feedback-description {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 消费影响力卡片 */
.marketing-influence-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.influence-metrics {
    margin: 20px 0;
}

.influence-item {
    margin-bottom: 25px;
}

.influence-item:last-child {
    margin-bottom: 0;
}

.influence-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.star-rating {
    display: flex;
    gap: 4px;
    margin-bottom: 10px;
}

.star-rating i {
    font-size: 16px;
    color: #ddd;
    transition: color 0.2s;
}

.star-rating i.active {
    color: #FFD700;
}

.influence-description {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 内容数据页面样式 */
.content-data-section {
    display: block;
}

/* 内容指标网格 */
.content-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.content-metric-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: 50px 1fr 80px;
    gap: 15px;
    align-items: center;
    transition: transform 0.2s, box-shadow 0.2s;
}

.content-metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #FF6B35, #FF8A65);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.metric-icon i {
    color: white;
    font-size: 20px;
}

.metric-content {
    flex: 1;
}

.metric-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.metric-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    margin-bottom: 8px;
    display: inline-block;
}

.metric-status.excellent {
    background-color: #e8f5e8;
    color: #4caf50;
}

.metric-status.moderate {
    background-color: #fff3cd;
    color: #856404;
}

.metric-description {
    font-size: 10px;
    color: #999;
    line-height: 1.3;
}

.metric-chart {
    width: 80px;
    height: 80px;
    position: relative;
}

/* 互动趋势分析 */
.interaction-trends-section {
    margin-top: 30px;
}

.trends-chart-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.chart-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.trend-legend {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #666;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-color.views {
    background-color: #4285F4;
}

.legend-color.likes {
    background-color: #34A853;
}

.legend-color.comments {
    background-color: #FF6B35;
}

.trend-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.stat-label {
    font-size: 11px;
    color: #999;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.trends-chart-wrapper {
    height: 400px;
    position: relative;
}

/* 内容与兴趣分析模块 */
.content-interest-section {
    margin-top: 30px;
}

.content-interest-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    margin-top: 20px;
}

/* 内容分布图表卡片 */
.content-distribution-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-chart-container {
    height: 300px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 内容详情卡片 */
.content-details-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-details-list {
    margin: 20px 0;
}

.content-detail-item {
    display: grid;
    grid-template-columns: 120px 1fr 60px;
    gap: 15px;
    align-items: flex-start;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.content-detail-item:last-child {
    border-bottom: none;
}

.content-detail-item.highlighted {
    background-color: #f8f9ff;
    border-color: #4285F4;
    border-radius: 8px;
    padding: 15px 12px;
    margin: 0 -12px;
    transition: all 0.3s ease;
}

.content-category {
    display: flex;
    align-items: center;
    gap: 8px;
}

.category-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.category-icon.automotive {
    background-color: #4285F4;
}

.category-icon.mobile {
    background-color: #FF6B35;
}

.category-icon.family {
    background-color: #34A853;
}

.category-icon.beauty {
    background-color: #EA4335;
}

.category-icon.vlog {
    background-color: #9C27B0;
}

.category-name {
    font-size: 13px;
    font-weight: 500;
    color: #333;
}

.content-description {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.content-percentage {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-align: right;
}

/* 发布分析模块 */
.publishing-analysis-section {
    margin-top: 30px;
}

.publishing-analysis-grid {
    display: grid;
    grid-template-columns: 0.85fr 1.15fr;
    gap: 24px;
    margin-top: 20px;
}

/* 发布日历 */
.publishing-calendar-card {
    background: white;
    border-radius: 12px;
    padding: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.calendar-container {
    margin: 16px 0 12px 0;
}

.calendar-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    gap: 16px;
}

.nav-btn {
    background: none;
    border: none;
    color: #999;
    font-size: 12px;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background-color: #f5f5f5;
    color: #333;
}

.month-display {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    min-width: 70px;
    text-align: center;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 6px;
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 6px 3px;
}

.weekday {
    text-align: center;
    font-size: 10px;
    font-weight: 500;
    color: #666;
    padding: 3px 1px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 3px;
    background-color: #f8f9fa;
    padding: 6px;
    border-radius: 4px;
}

.calendar-date {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: white;
    color: #333;
    min-height: 24px;
    max-height: 24px;
}

.calendar-date:hover {
    background-color: #f0f0f0;
}

.calendar-date.has-content {
    background-color: #FF6B35;
    color: white;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(255, 107, 53, 0.3);
}

.calendar-date.other-month {
    color: #ccc;
    background-color: #fafafa;
}

/* 发布频率 */
.publishing-frequency-card {
    background: white;
    border-radius: 12px;
    padding: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.frequency-stats-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 16px 0 18px 0;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.frequency-summary {
    display: flex;
    gap: 24px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.summary-number {
    font-size: 20px;
    font-weight: 700;
    color: #FF6B35;
    line-height: 1;
}

.summary-label {
    font-size: 10px;
    color: #666;
    margin-top: 3px;
    white-space: nowrap;
}

.frequency-date {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 3px;
}

.date-text {
    font-size: 11px;
    color: #333;
    font-weight: 500;
}

.date-badge {
    background-color: #4CAF50;
    color: white;
    padding: 1px 6px;
    border-radius: 8px;
    font-size: 9px;
    font-weight: 500;
}

.date-description {
    font-size: 9px;
    color: #999;
    white-space: nowrap;
}

.frequency-chart-container {
    height: 160px;
    position: relative;
    margin: 16px 0 12px 0;
    padding: 0 8px;
}

/* 标签分析模块 */
.tags-analysis-section {
    margin-top: 30px;
}

/* 标签计数头部 */
.tags-count-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    padding: 0 4px;
}

.tags-count {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.tags-info-icon {
    font-size: 12px;
    color: #999;
    cursor: pointer;
}

/* 标签云区域 */
.tags-cloud-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tags-analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

/* 标签云 */
.tags-cloud-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tags-cloud-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    line-height: 2;
    min-height: 220px;
    align-content: flex-start;
    justify-content: center;
    align-items: baseline;
    padding: 10px 0;
}

.tag-item {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    white-space: nowrap;
    transition: all 0.2s ease;
    cursor: pointer;
}

.tag-item:hover {
    transform: scale(1.05);
    opacity: 0.8;
}

/* 标签大小分级 - 6级词云效果 */
.tag-item.size-1 {
    font-size: 9px;
    color: #999;
    background: transparent;
    font-weight: 400;
}

.tag-item.size-2 {
    font-size: 12px;
    color: #777;
    background: transparent;
    font-weight: 450;
}

.tag-item.size-3 {
    font-size: 15px;
    color: #5a90e2;
    background: transparent;
    font-weight: 500;
}

.tag-item.size-4 {
    font-size: 18px;
    color: #3c7bd9;
    background: transparent;
    font-weight: 600;
}

.tag-item.size-5 {
    font-size: 21px;
    color: #2563eb;
    background: transparent;
    font-weight: 700;
}

.tag-item.size-6 {
    font-size: 24px;
    color: #1d4ed8;
    background: transparent;
    font-weight: 800;
}

/* 网红类别卡片 */
.category-analysis-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 280px;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px 0;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.category-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.category-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.category-bar {
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.category-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.category-bar.entertainment .category-fill {
    background: #4285f4;
}

.category-bar.movie .category-fill {
    background: #ff9500;
}

.category-bar.kids .category-fill {
    background: #34a853;
}

.category-bar.education .category-fill {
    background: #fbbc04;
}

.category-percentage {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    min-width: 40px;
    text-align: right;
}

/* Top标签列表 */
.top-tags-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 280px;
}

.top-tags-list {
    margin: 20px 0;
}

.top-tag-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tag-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.tag-dot.blue {
    background: #4285f4;
}

.tag-dot.orange {
    background: #ff9500;
}

.tag-dot.green {
    background: #34a853;
}

.tag-info {
    flex: 1;
}

.tag-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.tag-percentage {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    min-width: 40px;
    text-align: right;
}

.tag-percentage {
    font-size: 16px;
    font-weight: 700;
    color: #FF6B35;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .audience-analysis-grid,
    .demographics-grid,
    .marketing-analysis-grid,
    .content-interest-grid,
    .tags-analysis-grid,
    .brand-overview-grid {
        grid-template-columns: 1fr;
    }

    .publishing-analysis-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .content-detail-item {
        grid-template-columns: 100px 1fr 50px;
        gap: 10px;
    }

    .content-metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .audience-overview-grid {
        grid-template-columns: 1fr;
    }

    .audience-stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-icon i {
        font-size: 16px;
    }

    .region-item,
    .language-item {
        grid-template-columns: 60px 1fr 50px;
        gap: 8px;
    }

    .region-label,
    .language-label {
        font-size: 11px;
    }

    .region-bar,
    .language-bar {
        height: 18px;
    }

    .content-detail-item {
        grid-template-columns: 80px 1fr 40px;
        gap: 8px;
        padding: 12px 0;
    }

    .category-name {
        font-size: 11px;
    }

    .content-description {
        font-size: 11px;
    }

    .content-percentage {
        font-size: 12px;
    }

    .content-metrics-grid {
        grid-template-columns: 1fr;
    }

    .content-metric-card {
        grid-template-columns: 40px 1fr 60px;
        gap: 10px;
        padding: 15px;
    }

    .metric-icon {
        width: 40px;
        height: 40px;
    }

    .metric-icon i {
        font-size: 16px;
    }

    .metric-value {
        font-size: 20px;
    }

    .chart-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .trend-legend {
        gap: 15px;
    }

    .trend-stats {
        gap: 15px;
    }

    .publishing-analysis-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .publishing-calendar-card,
    .publishing-frequency-card {
        padding: 15px;
    }

    .calendar-navigation {
        gap: 12px;
        margin-bottom: 12px;
    }

    .month-display {
        font-size: 12px;
        min-width: 60px;
    }

    .nav-btn {
        padding: 4px;
        font-size: 10px;
        width: 20px;
        height: 20px;
    }

    .weekday {
        font-size: 9px;
        padding: 2px 1px;
    }

    .calendar-date {
        font-size: 9px;
        min-height: 20px;
        max-height: 20px;
    }

    .frequency-stats-header {
        flex-direction: column;
        gap: 10px;
        align-items: center;
        padding: 10px 12px;
        margin: 12px 0 14px 0;
    }

    .frequency-summary {
        gap: 16px;
    }

    .summary-number {
        font-size: 16px;
    }

    .summary-label {
        font-size: 9px;
    }

    .frequency-date {
        align-items: center;
    }

    .date-text {
        font-size: 10px;
    }

    .date-badge {
        font-size: 8px;
        padding: 1px 5px;
    }

    .date-description {
        font-size: 8px;
    }

    .frequency-chart-container {
        height: 120px;
        padding: 0 4px;
        margin: 12px 0 8px 0;
    }

    .tags-cloud-container {
        min-height: 150px;
        padding: 15px 0;
    }

    .tag-item.size-1 {
        font-size: 8px;
    }

    .tag-item.size-2 {
        font-size: 10px;
    }

    .tag-item.size-3 {
        font-size: 13px;
    }

    .tag-item.size-4 {
        font-size: 15px;
    }

    .tag-item.size-5 {
        font-size: 18px;
    }

    .tag-item.size-6 {
        font-size: 20px;
    }

    .tags-count-header {
        margin-bottom: 15px;
    }

    .tags-cloud-section {
        padding: 15px;
        margin-bottom: 15px;
    }

    .tags-cloud-container {
        min-height: 150px;
        gap: 6px;
    }

    .category-analysis-card,
    .top-tags-card {
        padding: 15px;
    }

    .category-list,
    .top-tags-list {
        padding: 15px 0;
        gap: 12px;
    }

    .top-tag-item {
        gap: 8px;
    }

    .tag-dot {
        width: 6px;
        height: 6px;
    }

    .tag-name,
    .category-name {
        font-size: 12px;
    }

    .tag-percentage,
    .category-percentage {
        font-size: 12px;
    }

    .tag-item.size-1 {
        font-size: 7px;
    }

    .tag-item.size-2 {
        font-size: 9px;
    }

    .tag-item.size-3 {
        font-size: 11px;
    }

    .tag-item.size-4 {
        font-size: 13px;
    }

    .tag-item.size-5 {
        font-size: 15px;
    }

    .tag-item.size-6 {
        font-size: 17px;
    }

    /* 品牌数据移动端样式 */
    .brand-stat-card {
        padding: 15px;
    }

    .brand-stat-card .stat-number {
        font-size: 28px;
    }

    .trend-chart-container {
        height: 200px;
    }

    .trend-summary {
        gap: 30px;
        padding: 12px;
    }

    .trend-value {
        font-size: 16px;
    }
}

/* 品牌提及模块 */
.brand-mention-section {
    margin-bottom: 30px;
}

.brand-mention-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

/* 提及模块头部 */
.mention-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.mention-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.mention-search {
    position: relative;
    width: 280px;
}

.search-input {
    width: 100%;
    padding: 8px 40px 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 13px;
    color: #666;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #FF6B35;
    background: white;
}

.search-input::placeholder {
    color: #999;
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 12px;
}

/* 品牌提及表格 */
.mention-table {
    width: 100%;
}

.table-header {
    display: grid;
    grid-template-columns: 220px 90px 110px 130px 130px 140px 50px;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 12px;
}

.header-item {
    font-size: 13px;
    font-weight: 500;
    color: #666;
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.header-item:hover {
    color: #FF6B35;
}

.header-item i {
    font-size: 10px;
    opacity: 0.6;
}

.table-body {
    display: block;
}

.table-row {
    display: grid;
    grid-template-columns: 220px 90px 110px 130px 130px 140px 50px;
    gap: 16px;
    padding: 20px 0;
    border-bottom: 1px solid #f8f9fa;
    align-items: center;
    transition: background-color 0.2s ease;
}

.table-row:hover {
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 0 -16px;
    padding: 20px 16px;
}

.table-row:last-child {
    border-bottom: none;
}

/* 品牌信息单元格 */
.brand-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.brand-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.brand-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.brand-info {
    flex: 1;
    min-width: 0;
}

.brand-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.brand-url {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 数据单元格 */
.data-cell {
    font-size: 13px;
    color: #333;
    font-weight: 500;
}

/* 数字单元格右对齐 */
.number-cell {
    text-align: right;
}

/* 日期单元格 */
.date-cell {
    color: #666;
    font-size: 13px;
}

/* 用户头像单元格 */
.user-avatar-cell {
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

/* 操作单元格 */
.action-cell {
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-icon {
    color: #999;
    font-size: 12px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.info-icon:hover {
    color: #FF6B35;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 13px;
    color: #666;
}

.current-page {
    color: #FF6B35;
    font-weight: 600;
}

.page-separator,
.page-number {
    color: #999;
    cursor: pointer;
    transition: color 0.2s ease;
}

.page-separator:hover,
.page-number:hover {
    color: #FF6B35;
}

.page-dots {
    color: #ccc;
}

.total-pages {
    color: #666;
    font-size: 12px;
}

/* 品牌提及移动端样式 */
@media (max-width: 768px) {
    .brand-mention-card {
        padding: 15px;
    }

    .mention-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .mention-search {
        width: 100%;
    }

    .mention-table {
        overflow-x: auto;
    }

    .table-header,
    .table-row {
        min-width: 800px;
        grid-template-columns: 180px 80px 100px 110px 110px 120px 50px;
    }

    .brand-cell {
        min-width: 120px;
    }

    .data-cell {
        min-width: 70px;
        font-size: 12px;
    }

    .action-cell {
        min-width: 40px;
    }

    .brand-name {
        font-size: 12px;
    }

    .brand-url {
        font-size: 10px;
    }

    .user-avatar-cell {
        min-width: 50px;
    }

    .user-avatar {
        width: 28px;
        height: 28px;
    }
}

/* ==================== 品牌数据页签样式 ==================== */

/* 品牌数据主容器 */
.brand-content {
    padding: 0;
}

/* 基本数据概览 */
.brand-overview-section {
    margin-bottom: 30px;
}

.brand-overview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.brand-stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.brand-stat-card:hover {
    transform: translateY(-2px);
}

.brand-stat-card .stat-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.brand-stat-card .stat-main {
    margin-bottom: 15px;
}

.brand-stat-card .stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #FF6B35;
    line-height: 1;
    margin-bottom: 5px;
}

.brand-stat-card .stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.brand-stat-card .stat-description {
    font-size: 13px;
    color: #888;
    line-height: 1.5;
}



/* 合作历史趋势 */
.brand-trend-section {
    margin-bottom: 30px;
}

.brand-trend-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.trend-chart-container {
    height: 300px;
    margin: 20px 0;
}

.trend-summary {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.trend-stat {
    text-align: center;
}

.trend-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.trend-value {
    font-size: 16px;
    font-weight: 700;
    color: #333;
}



/* 基本数据区域 */
.basic-data {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.section-title i {
    color: #FF6B35;
}

.time-filter {
    margin-left: auto;
    font-size: 12px;
    color: #999;
    font-weight: normal;
}

/* 数据网格 */
.data-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
}

.data-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.data-card h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    font-weight: normal;
}

.data-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.data-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.data-change.positive {
    color: #52c41a;
}

.data-change.negative {
    color: #ff4d4f;
}

.data-subtitle {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

/* 增长数据区域 */
.growth-data {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.growth-cards {
    display: flex;
    gap: 20px;
}

.growth-card {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.growth-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.growth-icon.blue {
    background-color: #1890ff;
}

.growth-icon.green {
    background-color: #52c41a;
}

.growth-icon.orange {
    background-color: #fa8c16;
}

.growth-info .growth-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.growth-info .growth-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 增长数据标签和图表控制 */
.growth-tabs-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30px 0 20px 0;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.growth-tabs {
    display: flex;
    gap: 0;
}

.growth-tab {
    padding: 10px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    position: relative;
    transition: all 0.3s ease;
}

.growth-tab.active {
    color: #FF6B35;
}

.growth-tab.active::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #FF6B35;
}

.growth-tab:hover:not(.active) {
    color: #333;
}

.chart-controls {
    display: flex;
    gap: 5px;
}



/* 图表容器 */
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chart-container canvas {
    max-height: 100%;
}



/* 频道质量区域 */
.channel-quality {
    margin-bottom: 20px;
}

/* Bento Grid 卡片布局 */
.quality-bento-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
    margin-top: 20px;
}

/* 基础卡片样式 */
.quality-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.quality-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

/* 卡片布局定义 */
.score-card {
    grid-column: 1;
    grid-row: 1;
}

.radar-chart-card {
    grid-column: 2;
    grid-row: 1;
}

.detailed-scores-card {
    grid-column: 3;
    grid-row: 1;
}

.cooperation-score-card {
    grid-column: 1;
    grid-row: 2;
}

.cooperation-tags-card {
    grid-column: 2 / -1;
    grid-row: 2;
}

/* 卡片头部样式 */
.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.card-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 综合评分卡片 */
.score-card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 280px;
}

.score-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.score-badge.excellent {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.score-display {
    display: flex;
    align-items: baseline;
    margin-bottom: 10px;
}

.score-number {
    font-size: 36px;
    font-weight: 700;
    color: #333;
}

.score-suffix {
    font-size: 16px;
    color: #666;
    margin-left: 4px;
}

.star-rating {
    display: flex;
    gap: 2px;
    margin-bottom: 15px;
}

.star-rating i {
    color: #fadb14;
    font-size: 16px;
}

.card-description {
    font-size: 12px;
    color: #666;
    line-height: 1.6;
    margin: 0;
    margin-top: auto;
}

/* 雷达图卡片 */
.radar-chart-card {
    display: flex;
    flex-direction: column;
    text-align: center;
    min-height: 280px;
}

.radar-container {
    position: relative;
    height: 220px;
    width: 220px;
    margin: 0 auto;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.radar-container canvas {
    max-width: 100%;
    max-height: 100%;
}

/* 详细指标评分卡片 */
.detailed-scores-card {
    min-height: 280px;
}

.detailed-scores {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.score-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.score-item:last-child {
    border-bottom: none;
}

.score-label {
    font-size: 14px;
    color: #333;
    flex: 1;
}

.score-stars {
    display: flex;
    gap: 2px;
    margin: 0 15px;
}

.score-stars i {
    font-size: 14px;
    color: #fadb14;
}

.score-stars .far {
    color: #d9d9d9;
}

.score-level {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.score-level.excellent {
    background-color: #f6ffed;
    color: #52c41a;
}

.score-level.good {
    background-color: #e6f7ff;
    color: #1890ff;
}

.score-level.average {
    background-color: #fff7e6;
    color: #fa8c16;
}

/* 合作指向卡片 */
.cooperation-score-card {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.cooperation-rating {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin: 15px 0;
}

.cooperation-number {
    font-size: 36px;
    font-weight: 700;
    color: #FF6B35;
}

.cooperation-divider {
    font-size: 24px;
    color: #999;
}

.cooperation-total {
    font-size: 24px;
    color: #999;
}

.cooperation-description,
.cooperation-note {
    font-size: 12px;
    color: #666;
    margin: 4px 0;
    line-height: 1.4;
}

/* 合作行为标签卡片 */
.cooperation-tags-card {
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

.tags-indicator {
    background-color: #fff7e6;
    color: #fa8c16;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}



.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.tag {
    background-color: #f0f0f0;
    color: #666;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.tag:hover {
    background-color: #ffd591;
    color: #d46b08;
}

/* 分页组件 */
.pagination-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 20px;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 4px;
}

.pagination-tab {
    flex: 1;
    padding: 8px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.pagination-tab.active {
    background-color: white;
    color: #333;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.pagination-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #f5f5f5;
    border-color: #FF6B35;
}

.pagination-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.pagination-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.page-number:hover {
    background-color: #f5f5f5;
    border-color: #FF6B35;
}

.page-number.active {
    background-color: #FF6B35;
    border-color: #FF6B35;
    color: white;
}

/* 动画 */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-transition {
    transition: opacity 0.3s ease;
}

.fade-out {
    opacity: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .data-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .growth-cards {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar {
        position: relative;
        width: 100%;
        height: auto;
        top: 0;
    }

    .main-content {
        margin-left: 0;
    }



    /* 网红信息卡片响应式 */
    .influencer-content {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .influencer-left {
        width: 100%;
    }

    .influencer-right {
        width: 100%;
    }

    .action-buttons {
        flex-wrap: wrap;
        gap: 8px;
    }

    /* 关键指标卡片响应式 */
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    /* 数据标签页响应式 */
    .tab-buttons {
        flex-wrap: wrap;
        gap: 8px;
        border-bottom: none;
    }

    .tab-button {
        border: 1px solid #ddd;
        border-radius: 6px;
        border-bottom: 2px solid transparent;
    }

    .tab-button.active {
        border-color: #FF6B35;
        background-color: #FF6B35;
        color: white;
    }

    .data-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .pagination-controls {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .data-grid {
        grid-template-columns: 1fr;
    }

    /* 小屏幕响应式 */
    .influencer-left {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .name-row {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .location-row {
        justify-content: center;
        flex-wrap: wrap;
    }

    .action-buttons {
        justify-content: center;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .growth-tabs-container {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .growth-tabs {
        width: 100%;
        justify-content: space-between;
    }

    .growth-tab {
        flex: 1;
        text-align: center;
        padding: 8px 10px;
        font-size: 12px;
    }

    .chart-container {
        height: 300px;
        padding: 15px;
    }

    .quality-bento-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .score-card,
    .radar-chart-card,
    .detailed-scores-card,
    .cooperation-score-card,
    .cooperation-tags-card {
        grid-column: 1;
        grid-row: auto;
        min-height: auto;
    }

    .cooperation-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .radar-container {
        height: 180px;
        width: 180px;
    }

    /* 合作价格和排名区域响应式 */
    .pricing-grid,
    .ranking-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .ranking-card {
        min-height: 100px;
        padding: 15px;
    }

    .ranking-icon {
        width: 40px;
        height: 40px;
    }

    .ranking-icon i {
        font-size: 16px;
    }

    .rank-number {
        font-size: 24px;
    }

    .cpm-value .amount,
    .pricing-value .amount-range {
        font-size: 20px;
    }
}
