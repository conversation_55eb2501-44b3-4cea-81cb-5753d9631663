# 达人详情页 2.0 增强需求文档

## 1. 项目概述

### 1.1 背景
基于现有达人详情页功能，对标NOX Influencer平台，全面升级达人数据展示和分析功能，为用户提供更加丰富、直观、有价值的达人信息，提升商业决策效率。

### 1.2 目标
- 提供全方位的达人数据展示（基础信息、受众分析、内容表现、商业价值）
- 实现智能化的品牌合作分析
- 支持多平台数据整合展示
- 优化用户体验和数据可视化

### 1.3 适用范围
本文档适用于YouTube、TikTok、Instagram等主流社交媒体平台的达人详情页功能开发。

---

## 2. 页面整体结构

### 2.1 页面布局
```
┌─────────────────────────────────────────────────────┐
│                   顶部导航栏                          │
├─────────────────────────────────────────────────────┤
│              达人信息总览卡片（固定）                  │
├─────────────────────────────────────────────────────┤
│    数据总览 │ 受众数据 │ 内容数据 │ 品牌数据         │
├─────────────────────────────────────────────────────┤
│                                                      │
│                  页签内容区域                         │
│                                                      │
└─────────────────────────────────────────────────────┘
```

### 2.2 响应式设计要求
- PC端（≥1200px）：完整展示所有功能
- 平板端（768px-1199px）：适当调整布局，保持核心功能
- 移动端（<768px）：垂直布局，优先展示关键信息

---

## 3. 达人信息总览卡片

### 3.1 功能描述
固定在页面顶部的信息展示区域，不随页签切换而变化，展示达人的核心信息和关键指标。

### 3.2 布局设计

#### 3.2.1 左侧信息区
| 元素 | 说明 | 数据来源 |
|------|------|----------|
| 达人头像 | 圆形，60x60px | `influencers.avatar` |
| 达人名称 | 主标题样式 | `influencers.name` |
| 平台标识 | 平台图标 | `platforms.platform` |
| 地区语言 | 国旗+国家名，语言标签 | `influencers.country`, `influencers.language` |
| 认证标识 | 官方认证图标（如适用） | `influencers.isVerified` |
| 访问链接 | "访问主页"按钮 | `platforms.profileUrl` |

#### 3.2.2 右侧指标区（横向排列）

**指标卡片设计：**
```
┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│   粉丝数    │ │ 最近发布    │ │ 最近推广    │ │ 综合评分    │ │  合作指数   │
│   4.14亿    │ │   7天前     │ │   13天前    │ │   4.56分    │ │    6/10     │
└─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘
```

### 3.3 数据获取与计算

#### 3.3.1 粉丝数
- **数据来源**：`platforms.subscribers`
- **更新频率**：实时
- **显示规则**：
  - < 10,000：显示具体数字
  - 10K-999K：显示 "xx.xK"
  - ≥ 1M：显示 "xx.xM" 或 "x.x亿"（中文环境）

#### 3.3.2 最近发布时间
- **计算SQL**：
```sql
SELECT DATEDIFF(NOW(), MAX(publishDate)) as days_ago
FROM contents 
WHERE influencerId = ? AND platform = ?
```
- **显示规则**：
  - 0天：显示"今天"
  - 1-6天：显示"x天前"
  - 7-29天：显示"x周前"
  - 30-365天：显示"x个月前"
  - >365天：显示"x年前"

#### 3.3.3 最近推广时间
- **计算SQL**：
```sql
SELECT DATEDIFF(NOW(), MAX(publishDate)) as days_ago
FROM contents 
WHERE influencerId = ? AND platform = ? AND isPromoted = 1
```
- **显示规则**：
  - 有记录：同最近发布时间规则
  - 无记录：显示"暂无推广"

#### 3.3.4 综合评分
- **数据来源**：`platforms.noxScore`（预计算字段）
- **评分范围**：1-5分
- **显示方式**：数字 + 星级图标
- **计算公式**：详见[4.2 综合评分计算规则](#42-综合评分计算规则)

#### 3.3.5 合作指数
- **数据来源**：`platforms.collaborationTendency`（预计算字段）
- **评分范围**：1-10分
- **显示方式**：分数 + 进度条
- **计算公式**：详见[4.3 合作指数计算规则](#43-合作指数计算规则)

---

## 4. 数据总览页签

### 4.1 功能模块划分
1. 基本数据统计
2. 增长趋势分析
3. 频道质量评估
4. 商业价值评估
5. 排名信息

### 4.2 综合评分计算规则

#### 4.2.1 评分维度与权重
| 维度 | 权重 | 说明 | 计算方法 |
|------|------|------|----------|
| 粉丝增长 | 20% | 近30天粉丝增长率 | `log10(增长率*1000) + 1`，上限5分 |
| 创作频率 | 15% | 平均日发布数 | `日均发布数 * 2`，上限5分 |
| 内容质量 | 20% | 观看量/粉丝数比 | `比值 * 5`，上限5分 |
| 互动率 | 20% | 综合互动表现 | `互动率 * 50`，上限5分 |
| 粉丝质量 | 15% | 真实粉丝评估 | LLM分析得出，1-5分 |
| 合作历史 | 10% | 历史合作次数 | `合作次数 / 5`，上限5分 |

#### 4.2.2 综合计算公式
```
noxScore = (
    followerGrowthScore * 0.2 + 
    publishFrequencyScore * 0.15 + 
    contentQualityScore * 0.2 + 
    engagementScore * 0.2 + 
    authenticityScore * 0.15 + 
    collaborationScore * 0.1
)
```

#### 4.2.3 等级划分
- 4.5-5.0分：优秀（绿色标识）
- 3.5-4.4分：良好（蓝色标识）
- 2.5-3.4分：中等（橙色标识）
- 1.0-2.4分：较差（红色标识）

### 4.3 合作指数计算规则

#### 4.3.1 评估因子与权重
| 因子 | 权重 | 计算方法 |
|------|------|----------|
| 活跃度 | 30% | `min(1, 近7天发布数/3)` |
| 响应率 | 25% | 历史邮件回复率 |
| 合作历史 | 20% | `min(1, 历史合作次数/10)` |
| 联系方式 | 15% | 有联系方式=1，无=0.3 |
| 受众质量 | 10% | `authenticityScore / 5` |

#### 4.3.2 计算公式
```
collaborationTendency = (
    activityFactor * 0.3 + 
    responseFactor * 0.25 + 
    cooperationHistory * 0.2 + 
    contactAvailability * 0.15 + 
    audienceQuality * 0.1
) * 10
```

### 4.4 基本数据模块

#### 4.4.1 数据卡片内容
| 指标 | 说明 | 计算方法 |
|------|------|----------|
| 平均观看量 | 近30个内容平均 | `AVG(views) WHERE publishDate >= DATE_SUB(NOW(), INTERVAL 30 DAY)` |
| 平均互动量 | 点赞+评论+分享 | `AVG(likes + comments + shares)` |
| 内容数量 | 统计期内发布数 | `COUNT(*) WHERE publishDate >= DATE_SUB(NOW(), INTERVAL 30 DAY)` |
| 观看/粉丝比 | 传播效率指标 | `平均观看量 / 粉丝数 * 100%` |
| 预计曝光量 | 潜在触达人数 | 基于历史数据的算法预测 |

#### 4.4.2 数据展示要求
- 每个指标显示当前值和环比变化
- 使用颜色标识变化趋势（上升绿色、下降红色）
- 支持点击查看详细计算说明

### 4.5 增长趋势模块

#### 4.5.1 图表类型
- **粉丝增长趋势**：面积图，展示累计粉丝数变化
- **播放量趋势**：柱状图，展示每日播放量
- **发布频率**：折线图，展示内容发布数量

#### 4.5.2 交互功能
- 时间范围选择：7天、30天、90天、1年
- 图表类型切换：折线图/柱状图
- 数据点详情：鼠标悬停显示具体数值

### 4.6 频道质量评估

#### 4.6.1 雷达图展示维度
```
        粉丝增长
           │
    创作频率├─┤互动率
           │
    内容质量├─┤粉丝质量
           │
        合作态度
```

#### 4.6.2 详细指标说明
| 指标 | 优秀标准 | 计算依据 |
|------|----------|----------|
| 粉丝增长 | 月增长>5% | 近30天粉丝变化率 |
| 创作频率 | >3次/周 | 平均发布间隔 |
| 内容质量 | 观看/粉丝>50% | 内容传播效率 |
| 互动率 | >5% | (点赞+评论)/观看 |
| 粉丝质量 | 真实度>80% | AI分析评估 |
| 合作态度 | 回复率>70% | 历史沟通数据 |

### 4.7 商业价值评估

#### 4.7.1 CPM定价模型
- **基础CPM**：
  - YouTube: $15
  - TikTok: $8
  - Instagram: $12
- **调整系数**：
  - 粉丝量系数：<10K(0.5), 10K-100K(1.0), 100K-1M(1.5), >1M(2.0)
  - 互动率系数：`min(2.0, max(0.5, 互动率 * 20))`
  - 质量系数：`noxScore / 3`
- **最终CPM** = 基础CPM × 粉丝量系数 × 互动率系数 × 质量系数

#### 4.7.2 视频植入价格
- **计算公式**：
  - 最低价 = CPM × 平均观看量 / 1000 × 0.8
  - 最高价 = CPM × 平均观看量 / 1000 × 1.2
- **显示格式**：$xx,xxx - $xx,xxx

### 4.8 排名信息
- **全球排名**：在所有同平台达人中的粉丝数排名
- **国家排名**：在同国家达人中的排名
- **类别排名**：在同内容类别中的排名（如有）

---

## 5. 受众数据页签

### 5.1 功能模块
1. 粉丝可信度评估
2. 地理分布分析
3. 人口统计分析
4. 营销价值分析
5. 内容兴趣分析

### 5.2 粉丝可信度评估

#### 5.2.1 评估指标
- **可信度评分**：1-5分制
- **真实粉丝比例**：百分比显示
- **粉丝质量等级**：优秀/良好/中等/较差

#### 5.2.2 粉丝类型分布
| 类型 | 说明 | 识别特征 |
|------|------|----------|
| 普通粉丝 | 真实活跃用户 | 正常互动行为 |
| 可疑粉丝 | 可能的机器人 | 异常互动模式 |
| 僵尸粉 | 不活跃账号 | 长期无互动 |
| 网红粉 | 其他创作者 | 认证账号关注 |

### 5.3 地理分布分析

#### 5.3.1 展示方式
- **世界地图**：热力图显示粉丝密度
- **国家排行**：Top 10国家条形图
- **城市分布**：主要城市分布（如数据可用）

#### 5.3.2 数据维度
- 国家/地区名称
- 粉丝数量
- 占比百分比
- 增长趋势

### 5.4 人口统计分析

#### 5.4.1 性别分布
- 环形图展示男女比例
- 显示具体百分比
- 对比行业平均值

#### 5.4.2 年龄分布
- 分组柱状图（13-17, 18-24, 25-34, 35-44, 45-54, 55-64, 65+）
- 支持性别交叉分析
- 标注主要年龄段

### 5.5 营销价值分析

#### 5.5.1 受众反馈指标
- **正向反馈率**：积极评论占比
- **推广接受度**：对商业内容的接受程度
- **购买意向**：通过评论分析得出

#### 5.5.2 影响力评估
- **推广吸引度**：1-5星评级
- **推广专业度**：1-5星评级
- **转化潜力**：高/中/低

### 5.6 内容兴趣分析

#### 5.6.1 兴趣分布图
- 环形图展示各兴趣类别占比
- 支持查看子类别详情
- 显示趋势变化

#### 5.6.2 兴趣类别
- 主要类别：美妆、时尚、科技、游戏、生活、美食等
- 每个类别显示：
  - 占比百分比
  - 观看时长
  - 互动率
  - 相关内容数

---

## 6. 内容数据页签

### 6.1 功能模块
1. 内容表现指标
2. 互动趋势分析
3. 发布规律分析
4. 标签词云分析
5. 内容列表展示

### 6.2 内容表现指标

#### 6.2.1 核心指标仪表盘
| 指标 | 计算公式 | 行业基准 | 展示方式 |
|------|----------|----------|----------|
| 互动率 | (点赞+评论+分享)/观看 | 0.59%-2.6% | 半圆仪表盘 |
| 观看/粉丝比 | 平均观看/粉丝数 | 0.13%-0.79% | 半圆仪表盘 |
| 点赞/观看比 | 点赞数/观看数 | 1.72%-4.41% | 半圆仪表盘 |
| 评论/观看比 | 评论数/观看数 | 0.04%-0.1% | 半圆仪表盘 |

#### 6.2.2 性能评级
- 优秀：高于行业75分位
- 良好：50-75分位
- 中等：25-50分位
- 较差：低于25分位

### 6.3 互动趋势分析

#### 6.3.1 多维度趋势图
- X轴：时间（支持日/周/月切换）
- Y轴：数值（支持绝对值/比率切换）
- 数据线：观看量、点赞数、评论数、分享数
- 支持独立显示/隐藏各数据线

#### 6.3.2 峰值分析
- 自动标注异常高峰
- 提供峰值原因分析（如爆款内容）

### 6.4 发布规律分析

#### 6.4.1 发布日历
- 月历视图，每日显示发布数量
- 颜色深度表示发布频率
- 点击查看当日发布内容

#### 6.4.2 发布时段分析
- 24小时分布热力图
- 星期分布柱状图
- 最佳发布时间建议

### 6.5 标签分析

#### 6.5.1 标签词云
- 字体大小反映使用频率
- 颜色区分标签类型
- 支持点击筛选相关内容

#### 6.5.2 Top标签列表
| 排名 | 标签 | 使用次数 | 占比 | 相关内容数 |
|------|------|----------|------|------------|
| 1 | #fashion | 156 | 11.4% | 45 |
| 2 | #beauty | 142 | 10.4% | 38 |
| ... | ... | ... | ... | ... |

### 6.6 内容列表

#### 6.6.1 统计信息
- 已收录作品总数
- 总观看量
- 总互动量
- 平均表现指标

#### 6.6.2 内容展示
- 网格布局（PC端4列，移动端2列）
- 每个内容显示：
  - 视频封面
  - 标题（截断显示）
  - 发布时间
  - 观看量、点赞数、评论数
  - 是否为推广内容标识

#### 6.6.3 筛选排序
- 时间筛选：最近7天/30天/90天/全部
- 类型筛选：全部/推广/非推广
- 排序方式：时间/观看量/互动率

---

## 7. 品牌数据页签

### 7.1 功能模块
1. 品牌合作概览
2. 推广效果对比
3. 品牌提及分析
4. 合作趋势分析

### 7.2 品牌合作概览

#### 7.2.1 关键指标
- **品牌广告效果**：推广内容平均互动率
- **发布频次**：每月推广内容数量
- **商业化率**：推广内容占比
- **平均合作价值**：基于CPM估算

### 7.3 推广效果对比

#### 7.3.1 对比维度
| 指标 | 推广内容 | 非推广内容 | 差异 |
|------|----------|------------|------|
| 平均观看量 | - | - | - |
| 平均互动率 | - | - | - |
| 完播率 | - | - | - |
| 分享率 | - | - | - |

#### 7.3.2 可视化展示
- 双柱对比图
- 趋势线对比
- 效果评分雷达图

### 7.4 品牌提及分析

#### 7.4.1 品牌列表
| 品牌 | Logo | 合作次数 | 互动率 | 总观看量 | 最近合作 | 预估价值 |
|------|------|----------|---------|----------|----------|----------|
| Victoria's Secret | [Logo] | 9 | 5.8% | 3450万 | 2024-10-23 | $7,776 |
| ... | ... | ... | ... | ... | ... | ... |

#### 7.4.2 品牌分析维度
- **提及类型**：
  - 正式合作（有推广标识）
  - 产品植入（无标识但有提及）
  - 自然提及（非商业提及）
- **合作形式**：
  - 独家视频
  - 产品评测
  - 开箱体验
  - 品牌活动

### 7.5 AI品牌识别（增强功能）

#### 7.5.1 识别范围
- 视频标题、描述文本
- 字幕内容（如可用）
- 视觉识别（Logo出现）
- 评论中的品牌讨论

#### 7.5.2 分析输出
```json
{
  "brand_mentions": [
    {
      "brand_name": "Nike",
      "mention_type": "explicit",
      "confidence": 0.95,
      "context": "在这个视频中我穿着Nike新款...",
      "sentiment": "positive"
    }
  ],
  "commercial_ratio": 0.15,
  "brand_diversity": 0.75
}
```

---

## 8. 技术实现方案

### 8.1 数据库扩展设计

#### 8.1.1 新增字段（platforms表）
```sql
ALTER TABLE platforms ADD COLUMN 
  lastPostTime DATETIME COMMENT '最近发布时间',
  lastPromoTime DATETIME COMMENT '最近推广时间',
  noxScore FLOAT COMMENT '综合评分',
  collaborationTendency INT COMMENT '合作倾向',
  avgViews30d INT COMMENT '30天平均观看',
  avgEngagement30d INT COMMENT '30天平均互动',
  estimatedCPM FLOAT COMMENT '预估CPM',
  estimatedVideoPrice VARCHAR(50) COMMENT '视频价格范围';
```

#### 8.1.2 新增表（brand_mentions）
```sql
CREATE TABLE brand_mentions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  influencerId VARCHAR(50),
  contentId VARCHAR(50),
  brandName VARCHAR(100),
  mentionType ENUM('explicit', 'implicit', 'product_placement'),
  confidence FLOAT,
  context TEXT,
  sentiment ENUM('positive', 'neutral', 'negative'),
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 8.2 API接口设计

#### 8.2.1 核心接口
```
GET /api/influencer/{id}/overview     # 获取总览数据
GET /api/influencer/{id}/audience     # 获取受众数据
GET /api/influencer/{id}/content      # 获取内容数据
GET /api/influencer/{id}/brands       # 获取品牌数据
POST /api/influencer/{id}/analyze     # 触发AI分析
```

#### 8.2.2 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-01-20T10:00:00Z"
}
```

### 8.3 缓存策略

#### 8.3.1 缓存层级
1. **浏览器缓存**：静态资源24小时
2. **CDN缓存**：图片、视频资源
3. **Redis缓存**：
   - 基础信息：5分钟
   - 统计数据：1小时
   - 分析结果：24小时

#### 8.3.2 缓存更新
- 主动更新：数据变更时清除相关缓存
- 被动更新：TTL过期自动更新

### 8.4 性能优化

#### 8.4.1 数据加载策略
- 关键数据优先加载
- 非关键数据懒加载
- 大数据集分页处理

#### 8.4.2 查询优化
- 建立必要索引
- 使用数据预聚合
- 避免N+1查询问题

---

## 9. 实施计划

### 9.1 开发阶段

#### 第一阶段（2周）
- [ ] 数据库结构设计与优化
- [ ] 达人总览卡片开发
- [ ] 数据总览页签基础功能

#### 第二阶段（3周）
- [ ] 受众数据分析功能
- [ ] 内容数据分析功能
- [ ] 数据可视化组件开发

#### 第三阶段（3周）
- [ ] 品牌数据分析功能
- [ ] AI品牌识别集成
- [ ] 性能优化与测试

#### 第四阶段（2周）
- [ ] 移动端适配
- [ ] 整体测试与bug修复
- [ ] 上线准备

### 9.2 里程碑
- M1：完成基础架构和数据总览（第2周）
- M2：完成所有数据分析模块（第6周）
- M3：完成AI功能集成（第8周）
- M4：正式上线（第10周）

---

## 10. 风险与应对

### 10.1 技术风险
| 风险 | 影响 | 应对措施 |
|------|------|----------|
| 数据量过大导致性能问题 | 页面加载缓慢 | 分页加载、缓存优化 |
| AI分析准确度不足 | 品牌识别错误 | 人工审核、模型优化 |
| 第三方API限制 | 数据更新受限 | 建立备用方案 |

### 10.2 业务风险
| 风险 | 影响 | 应对措施 |
|------|------|----------|
| 用户接受度低 | 功能使用率低 | 分阶段发布、收集反馈 |
| 数据准确性质疑 | 信任度下降 | 透明化算法、提供数据来源 |

---

## 11. 附录

### 11.1 名词解释
- **CPM**：Cost Per Mille，每千次展示成本
- **互动率**：(点赞+评论+分享)/观看量
- **完播率**：完整观看视频的用户比例
- **NoxScore**：综合评分算法

### 11.2 参考资料
- NOX Influencer平台功能分析
- 行业最佳实践案例
- 用户调研反馈报告

### 11.3 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2.0 | 2024-01-20 | 初始版本 | 产品团队 |

---

*本文档为达人详情页2.0版本的完整需求说明，如有疑问请联系产品团队。*